<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.dataflint</groupId>
        <artifactId>spark-parent</artifactId>
        <version>0.4.1-dxy</version>
    </parent>

    <artifactId>DataflintSparkExample324</artifactId>
    <packaging>jar</packaging>

    <name>Dataflint Spark Example 3.2.4</name>
    <description>Example project for Spark 3.2.4 with Dataflint plugin</description>

    <properties>
        <!-- Override parent Spark version for this specific example -->
        <spark.version>3.2.4</spark.version>
        <!-- Use Scala 2.13 for Spark 3.2.4 compatibility -->
        <scala.binary.version>2.13</scala.binary.version>
    </properties>

    <dependencies>
        <!-- Plugin dependency -->
        <dependency>
            <groupId>io.dataflint</groupId>
            <artifactId>spark</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spark Dependencies for 3.2.4 -->
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-core_${scala.binary.version}</artifactId>
            <version>${spark.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-sql_${scala.binary.version}</artifactId>
            <version>${spark.version}</version>
        </dependency>

        <!-- AWS SDK -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>${aws.sdk.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
            </plugin>
            
            <!-- Skip deployment for example modules -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
