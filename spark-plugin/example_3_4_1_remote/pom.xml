<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.dataflint</groupId>
        <artifactId>spark-parent</artifactId>
        <version>0.4.1-dxy</version>
    </parent>

    <artifactId>DataflintSparkExample341Remote</artifactId>
    <packaging>jar</packaging>

    <name>Dataflint Spark Example 3.4.1 Remote</name>
    <description>Remote example project for Spark 3.4.1 with Dataflint plugin</description>

    <properties>
        <!-- Override parent Spark version for this specific example -->
        <spark.version>3.4.1</spark.version>
        <!-- Use Scala 2.13 for Spark 3.4.1 compatibility -->
        <scala.binary.version>2.13</scala.binary.version>
    </properties>

    <dependencies>
        <!-- Spark Dependencies for 3.4.1 -->
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-core_${scala.binary.version}</artifactId>
            <version>${spark.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-sql_${scala.binary.version}</artifactId>
            <version>${spark.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
            </plugin>
            
            <!-- Skip deployment for example modules -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
