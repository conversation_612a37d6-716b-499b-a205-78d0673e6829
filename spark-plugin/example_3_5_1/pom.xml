<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.dataflint</groupId>
        <artifactId>spark-parent</artifactId>
        <version>0.4.1-dxy</version>
    </parent>

    <artifactId>DataflintSparkExample351</artifactId>
    <packaging>jar</packaging>

    <name>Dataflint Spark Example 3.5.1</name>
    <description>Example project for Spark 3.5.1 with Dataflint plugin</description>

    <properties>
        <!-- Override parent Spark version for this specific example -->
        <spark.version>3.5.1</spark.version>
        <!-- Use Scala 2.13 for Spark 3.5.1 compatibility -->
        <scala.binary.version>2.13</scala.binary.version>
    </properties>

    <dependencies>
        <!-- Plugin dependency -->
        <dependency>
            <groupId>io.dataflint</groupId>
            <artifactId>spark</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spark Dependencies for 3.5.1 -->
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-core_${scala.binary.version}</artifactId>
            <version>${spark.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-sql_${scala.binary.version}</artifactId>
            <version>${spark.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-streaming_${scala.binary.version}</artifactId>
            <version>${spark.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-sql-kafka-0-10_${scala.binary.version}</artifactId>
            <version>${spark.version}</version>
        </dependency>

        <!-- AWS SDK -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>${aws.sdk.version}</version>
        </dependency>

        <!-- Delta Lake -->
        <dependency>
            <groupId>io.delta</groupId>
            <artifactId>delta-spark_${scala.binary.version}</artifactId>
            <version>${delta.version}</version>
        </dependency>

        <!-- Iceberg for Spark 3.5 -->
        <dependency>
            <groupId>org.apache.iceberg</groupId>
            <artifactId>iceberg-spark-runtime-3.5_${scala.binary.version}</artifactId>
            <version>${iceberg.version}</version>
        </dependency>

        <!-- Scala Collection Compat -->
        <dependency>
            <groupId>org.scala-lang.modules</groupId>
            <artifactId>scala-collection-compat_${scala.binary.version}</artifactId>
        </dependency>

        <!-- ScalaTest -->
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${scala.binary.version}</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- SLF4J API -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.36</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
            </plugin>
            
            <!-- Skip deployment for example modules -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
