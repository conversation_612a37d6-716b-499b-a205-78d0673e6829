<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>io.dataflint</groupId>
    <artifactId>spark-parent</artifactId>
    <version>0.4.1-dxy</version>
    <packaging>pom</packaging>

    <name>Dataflint Spark Plugin Parent</name>
    <description>Open Source Data-Application Performance Monitoring for Apache Spark</description>
    <url>https://github.com/dataflint/spark</url>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>menishmueli</id>
            <name><PERSON><PERSON></name>
            <email><EMAIL></email>
            <url>http://dataflint.io</url>
        </developer>
    </developers>

    <scm>
        <connection>scm:git:**************:dataflint/spark.git</connection>
        <developerConnection>scm:git:**************:dataflint/spark.git</developerConnection>
        <url>https://github.com/dataflint/spark</url>
    </scm>

    <modules>
        <module>plugin</module>
        <module>example_3_1_3</module>
        <module>example_3_2_4</module>
        <module>example_3_3_3</module>
        <module>example_3_4_1</module>
        <module>example_3_5_1</module>
        <module>example_3_4_1_remote</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Scala versions -->
        <scala.version>2.13.12</scala.version>
        <scala.binary.version>2.13</scala.binary.version>
        
        <!-- Spark versions -->
        <spark.version>3.3.3</spark.version>
        
        <!-- Dependency versions -->
        <aws.sdk.version>1.12.470</aws.sdk.version>
        <iceberg.version>1.5.0</iceberg.version>
        <scala.collection.compat.version>2.11.0</scala.collection.compat.version>
        <scalatest.version>3.2.17</scalatest.version>
        <delta.version>3.1.0</delta.version>
        
        <!-- Plugin versions -->
        <scala.maven.plugin.version>4.8.1</scala.maven.plugin.version>
        <maven.compiler.plugin.version>3.11.0</maven.compiler.plugin.version>
        <maven.surefire.plugin.version>3.0.0</maven.surefire.plugin.version>
        <maven.source.plugin.version>3.2.1</maven.source.plugin.version>
        <maven.javadoc.plugin.version>3.4.1</maven.javadoc.plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spark Dependencies -->
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-core_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-sql_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-streaming_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.spark</groupId>
                <artifactId>spark-sql-kafka-0-10_${scala.binary.version}</artifactId>
                <version>${spark.version}</version>
            </dependency>

            <!-- AWS SDK -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws.sdk.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- Iceberg -->
            <dependency>
                <groupId>org.apache.iceberg</groupId>
                <artifactId>iceberg-spark-runtime-3.3_${scala.binary.version}</artifactId>
                <version>${iceberg.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- Scala Collection Compat -->
            <dependency>
                <groupId>org.scala-lang.modules</groupId>
                <artifactId>scala-collection-compat_${scala.binary.version}</artifactId>
                <version>${scala.collection.compat.version}</version>
            </dependency>

            <!-- Test Dependencies -->
            <dependency>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest_${scala.binary.version}</artifactId>
                <version>${scalatest.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Scala Maven Plugin -->
                <plugin>
                    <groupId>net.alchim31.maven</groupId>
                    <artifactId>scala-maven-plugin</artifactId>
                    <version>${scala.maven.plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>compile</goal>
                                <goal>testCompile</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <scalaVersion>${scala.version}</scalaVersion>
                        <args>
                            <arg>-target:jvm-1.8</arg>
                            <arg>-deprecation</arg>
                            <arg>-feature</arg>
                            <arg>-unchecked</arg>
                        </args>
                    </configuration>
                </plugin>

                <!-- Maven Compiler Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven.compiler.plugin.version}</version>
                    <configuration>
                        <source>8</source>
                        <target>8</target>
                    </configuration>
                </plugin>

                <!-- Maven Surefire Plugin for tests -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven.surefire.plugin.version}</version>
                    <configuration>
                        <skipTests>true</skipTests>
                    </configuration>
                </plugin>

                <!-- ScalaTest Maven Plugin -->
                <plugin>
                    <groupId>org.scalatest</groupId>
                    <artifactId>scalatest-maven-plugin</artifactId>
                    <version>2.2.0</version>
                    <configuration>
                        <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                        <junitxml>.</junitxml>
                        <filereports>TestSuiteReport.txt</filereports>
                    </configuration>
                    <executions>
                        <execution>
                            <id>test</id>
                            <goals>
                                <goal>test</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- Maven Source Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven.source.plugin.version}</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- Maven Javadoc Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven.javadoc.plugin.version}</version>
                    <executions>
                        <execution>
                            <id>attach-javadocs</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <doclint>none</doclint>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>https://nexus.k8s.qc.host.dxy/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>https://nexus.k8s.qc.host.dxy/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>nexus</id>
            <name>dxy private repository</name>
            <url>https://nexus.k8s.qc.host.dxy/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </releases>
            <layout>default</layout>
        </repository>
    </repositories>

</project>
